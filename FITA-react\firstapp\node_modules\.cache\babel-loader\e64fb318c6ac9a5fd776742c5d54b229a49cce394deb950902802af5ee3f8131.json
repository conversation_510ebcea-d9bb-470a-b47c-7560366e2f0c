{"ast": null, "code": "import logo from \"./logo.svg\";\nimport \"./App.css\";\nimport { Fragment as _Fragment, jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  let data = [{\n    albumId: 1,\n    id: 1,\n    title: \"accusamus beatae ad facilis cum similique qui sunt\",\n    url: \"https://via.placeholder.com/600/92c952\",\n    thumbnailUrl: \"https://via.placeholder.com/150/92c952\"\n  }, {\n    albumId: 1,\n    id: 2,\n    title: \"reprehenderit est deserunt velit ipsam\",\n    url: \"https://via.placeholder.com/600/771796\",\n    thumbnailUrl: \"https://via.placeholder.com/150/771796\"\n  }, {\n    albumId: 1,\n    id: 3,\n    title: \"officia porro iure quia iusto qui ipsa ut modi\",\n    url: \"https://via.placeholder.com/600/24f355\",\n    thumbnailUrl: \"https://via.placeholder.com/150/24f355\"\n  }, {\n    albumId: 1,\n    id: 4,\n    title: \"culpa odio esse rerum omnis laboriosam voluptate repudiandae\",\n    url: \"https://via.placeholder.com/600/d32776\",\n    thumbnailUrl: \"https://via.placeholder.com/150/d32776\"\n  }, {\n    albumId: 1,\n    id: 5,\n    title: \"natus nisi omnis corporis facere molestiae rerum in\",\n    url: \"https://via.placeholder.com/600/f66b97\",\n    thumbnailUrl: \"https://via.placeholder.com/150/f66b97\"\n  }, {\n    albumId: 1,\n    id: 6,\n    title: \"accusamus ea aliquid et amet sequi nemo\",\n    url: \"https://via.placeholder.com/600/56a8c2\",\n    thumbnailUrl: \"https://via.placeholder.com/150/56a8c2\"\n  }];\n  return /*#__PURE__*/_jsxDEV(_Fragment, {}, void 0, false);\n}\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["logo", "Fragment", "_Fragment", "jsxDEV", "_jsxDEV", "App", "data", "albumId", "id", "title", "url", "thumbnailUrl", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/FITA/FITA-react/firstapp/src/App.js"], "sourcesContent": ["import logo from \"./logo.svg\";\nimport \"./App.css\";\n\nfunction App() {\n  let data = [\n    {\n      albumId: 1,\n      id: 1,\n      title: \"accusamus beatae ad facilis cum similique qui sunt\",\n      url: \"https://via.placeholder.com/600/92c952\",\n      thumbnailUrl: \"https://via.placeholder.com/150/92c952\",\n    },\n    {\n      albumId: 1,\n      id: 2,\n      title: \"reprehenderit est deserunt velit ipsam\",\n      url: \"https://via.placeholder.com/600/771796\",\n      thumbnailUrl: \"https://via.placeholder.com/150/771796\",\n    },\n    {\n      albumId: 1,\n      id: 3,\n      title: \"officia porro iure quia iusto qui ipsa ut modi\",\n      url: \"https://via.placeholder.com/600/24f355\",\n      thumbnailUrl: \"https://via.placeholder.com/150/24f355\",\n    },\n    {\n      albumId: 1,\n      id: 4,\n      title: \"culpa odio esse rerum omnis laboriosam voluptate repudiandae\",\n      url: \"https://via.placeholder.com/600/d32776\",\n      thumbnailUrl: \"https://via.placeholder.com/150/d32776\",\n    },\n    {\n      albumId: 1,\n      id: 5,\n      title: \"natus nisi omnis corporis facere molestiae rerum in\",\n      url: \"https://via.placeholder.com/600/f66b97\",\n      thumbnailUrl: \"https://via.placeholder.com/150/f66b97\",\n    },\n    {\n      albumId: 1,\n      id: 6,\n      title: \"accusamus ea aliquid et amet sequi nemo\",\n      url: \"https://via.placeholder.com/600/56a8c2\",\n      thumbnailUrl: \"https://via.placeholder.com/150/56a8c2\",\n    },\n  ];\n  return <></>;\n}\n\nexport default App;\n"], "mappings": "AAAA,OAAOA,IAAI,MAAM,YAAY;AAC7B,OAAO,WAAW;AAAC,SAAAC,QAAA,IAAAC,SAAA,EAAAC,MAAA,IAAAC,OAAA;AAEnB,SAASC,GAAGA,CAAA,EAAG;EACb,IAAIC,IAAI,GAAG,CACT;IACEC,OAAO,EAAE,CAAC;IACVC,EAAE,EAAE,CAAC;IACLC,KAAK,EAAE,oDAAoD;IAC3DC,GAAG,EAAE,wCAAwC;IAC7CC,YAAY,EAAE;EAChB,CAAC,EACD;IACEJ,OAAO,EAAE,CAAC;IACVC,EAAE,EAAE,CAAC;IACLC,KAAK,EAAE,wCAAwC;IAC/CC,GAAG,EAAE,wCAAwC;IAC7CC,YAAY,EAAE;EAChB,CAAC,EACD;IACEJ,OAAO,EAAE,CAAC;IACVC,EAAE,EAAE,CAAC;IACLC,KAAK,EAAE,gDAAgD;IACvDC,GAAG,EAAE,wCAAwC;IAC7CC,YAAY,EAAE;EAChB,CAAC,EACD;IACEJ,OAAO,EAAE,CAAC;IACVC,EAAE,EAAE,CAAC;IACLC,KAAK,EAAE,8DAA8D;IACrEC,GAAG,EAAE,wCAAwC;IAC7CC,YAAY,EAAE;EAChB,CAAC,EACD;IACEJ,OAAO,EAAE,CAAC;IACVC,EAAE,EAAE,CAAC;IACLC,KAAK,EAAE,qDAAqD;IAC5DC,GAAG,EAAE,wCAAwC;IAC7CC,YAAY,EAAE;EAChB,CAAC,EACD;IACEJ,OAAO,EAAE,CAAC;IACVC,EAAE,EAAE,CAAC;IACLC,KAAK,EAAE,yCAAyC;IAChDC,GAAG,EAAE,wCAAwC;IAC7CC,YAAY,EAAE;EAChB,CAAC,CACF;EACD,oBAAOP,OAAA,CAAAF,SAAA,mBAAI,CAAC;AACd;AAACU,EAAA,GA9CQP,GAAG;AAgDZ,eAAeA,GAAG;AAAC,IAAAO,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}