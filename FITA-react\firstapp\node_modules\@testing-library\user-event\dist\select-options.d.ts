import { PointerOptions } from './utils';
declare const selectOptions: (args_0: Element, args_1: string | string[] | HTMLElement | HTMLElement[], args_2?: MouseEventInit | undefined, args_3?: PointerOptions | undefined) => void;
declare const deselectOptions: (args_0: Element, args_1: string | string[] | HTMLElement | HTMLElement[], args_2?: MouseEventInit | undefined, args_3?: PointerOptions | undefined) => void;
export { selectOptions, deselectOptions };
