{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\FITA\\\\FITA-react\\\\firstapp\\\\src\\\\App.js\";\nimport logo from \"./logo.svg\";\nimport \"./App.css\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: /*#__PURE__*/_jsxDEV(\"h1\", {\n      children: \"hiiii\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 7,\n      columnNumber: 7\n    }, this)\n  }, void 0, false);\n}\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["logo", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "App", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/FITA/FITA-react/firstapp/src/App.js"], "sourcesContent": ["import logo from \"./logo.svg\";\nimport \"./App.css\";\n\nfunction App() {\n  return (\n    <>\n      <h1>hiiii</h1>\n    </>\n  );\n}\n\nexport default App;\n"], "mappings": ";AAAA,OAAOA,IAAI,MAAM,YAAY;AAC7B,OAAO,WAAW;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEnB,SAASC,GAAGA,CAAA,EAAG;EACb,oBACEH,OAAA,CAAAE,SAAA;IAAAE,QAAA,eACEJ,OAAA;MAAAI,QAAA,EAAI;IAAK;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI;EAAC,gBACd,CAAC;AAEP;AAACC,EAAA,GANQN,GAAG;AAQZ,eAAeA,GAAG;AAAC,IAAAM,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}