[{"C:\\Users\\<USER>\\OneDrive\\Desktop\\FITA\\FITA-react\\firstapp\\src\\index.js": "1", "C:\\Users\\<USER>\\OneDrive\\Desktop\\FITA\\FITA-react\\firstapp\\src\\App.js": "2", "C:\\Users\\<USER>\\OneDrive\\Desktop\\FITA\\FITA-react\\firstapp\\src\\reportWebVitals.js": "3"}, {"size": 535, "mtime": 1756352020401, "results": "4", "hashOfConfig": "5"}, {"size": 1468, "mtime": 1756354593165, "results": "6", "hashOfConfig": "5"}, {"size": 362, "mtime": 1756352022187, "results": "7", "hashOfConfig": "5"}, {"filePath": "8", "messages": "9", "suppressedMessages": "10", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "xcfedb", {"filePath": "11", "messages": "12", "suppressedMessages": "13", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "14", "messages": "15", "suppressedMessages": "16", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\OneDrive\\Desktop\\FITA\\FITA-react\\firstapp\\src\\index.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\FITA\\FITA-react\\firstapp\\src\\App.js", ["17", "18"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\FITA\\FITA-react\\firstapp\\src\\reportWebVitals.js", [], [], {"ruleId": "19", "severity": 1, "message": "20", "line": 1, "column": 8, "nodeType": "21", "messageId": "22", "endLine": 1, "endColumn": 12}, {"ruleId": "19", "severity": 1, "message": "23", "line": 5, "column": 7, "nodeType": "21", "messageId": "22", "endLine": 5, "endColumn": 11}, "no-unused-vars", "'logo' is defined but never used.", "Identifier", "unusedVar", "'data' is assigned a value but never used."]